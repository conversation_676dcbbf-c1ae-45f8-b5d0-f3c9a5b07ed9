import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import '../../data/models/location_model.dart';
import '../../core/theme/app_theme.dart';

class MapWidget extends StatefulWidget {
  final LocationModel? initialLocation;
  final LocationModel? pickupLocation;
  final LocationModel? dropoffLocation;
  final Function(LocationModel)? onLocationSelected;
  final bool showCurrentLocation;
  final double height;

  const MapWidget({
    super.key,
    this.initialLocation,
    this.pickupLocation,
    this.dropoffLocation,
    this.onLocationSelected,
    this.showCurrentLocation = true,
    this.height = 300,
  });

  @override
  State<MapWidget> createState() => _MapWidgetState();
}

class _MapWidgetState extends State<MapWidget> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  LocationModel? _currentLocation;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    if (widget.showCurrentLocation) {
      await _getCurrentLocation();
    }
    _updateMarkers();
    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _getCurrentLocation() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      if (permission == LocationPermission.whileInUse ||
          permission == LocationPermission.always) {
        Position position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        );

        _currentLocation = LocationModel(
          latitude: position.latitude,
          longitude: position.longitude,
          name: 'Current Location',
        );

        if (_mapController != null) {
          await _mapController!.animateCamera(
            CameraUpdate.newLatLng(
              LatLng(position.latitude, position.longitude),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error getting current location: $e');
    }
  }

  void _updateMarkers() {
    _markers.clear();

    // Current location marker
    if (_currentLocation != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('current_location'),
          position: LatLng(_currentLocation!.latitude, _currentLocation!.longitude),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          infoWindow: const InfoWindow(title: 'Your Location'),
        ),
      );
    }

    // Pickup location marker
    if (widget.pickupLocation != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('pickup'),
          position: LatLng(
            widget.pickupLocation!.latitude,
            widget.pickupLocation!.longitude,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
          infoWindow: InfoWindow(
            title: 'Pickup Location',
            snippet: widget.pickupLocation!.address ?? 'Pickup Point',
          ),
        ),
      );
    }

    // Dropoff location marker
    if (widget.dropoffLocation != null) {
      _markers.add(
        Marker(
          markerId: const MarkerId('dropoff'),
          position: LatLng(
            widget.dropoffLocation!.latitude,
            widget.dropoffLocation!.longitude,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
          infoWindow: InfoWindow(
            title: 'Dropoff Location',
            snippet: widget.dropoffLocation!.address ?? 'Destination',
          ),
        ),
      );
    }

    // Draw route if both pickup and dropoff are available
    if (widget.pickupLocation != null && widget.dropoffLocation != null) {
      _drawRoute();
    }
  }

  void _drawRoute() {
    // Simple straight line route (in production, use Google Directions API)
    _polylines.add(
      Polyline(
        polylineId: const PolylineId('route'),
        points: [
          LatLng(widget.pickupLocation!.latitude, widget.pickupLocation!.longitude),
          LatLng(widget.dropoffLocation!.latitude, widget.dropoffLocation!.longitude),
        ],
        color: AppTheme.primaryColor,
        width: 4,
        patterns: [PatternItem.dash(20), PatternItem.gap(10)],
      ),
    );
  }

  void _onMapTap(LatLng position) {
    if (widget.onLocationSelected != null) {
      final location = LocationModel(
        latitude: position.latitude,
        longitude: position.longitude,
        name: 'Selected Location',
      );
      widget.onLocationSelected!(location);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        height: widget.height,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading map...'),
            ],
          ),
        ),
      );
    }

    final initialPosition = widget.initialLocation != null
        ? LatLng(widget.initialLocation!.latitude, widget.initialLocation!.longitude)
        : _currentLocation != null
            ? LatLng(_currentLocation!.latitude, _currentLocation!.longitude)
            : const LatLng(37.7749, -122.4194); // Default to San Francisco

    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: GoogleMap(
          onMapCreated: (GoogleMapController controller) {
            _mapController = controller;
          },
          initialCameraPosition: CameraPosition(
            target: initialPosition,
            zoom: 15.0,
          ),
          markers: _markers,
          polylines: _polylines,
          onTap: _onMapTap,
          myLocationEnabled: widget.showCurrentLocation,
          myLocationButtonEnabled: true,
          zoomControlsEnabled: false,
          mapToolbarEnabled: false,
          compassEnabled: true,
          trafficEnabled: false,
          buildingsEnabled: true,
          indoorViewEnabled: true,
          mapType: MapType.normal,
          style: _mapStyle,
        ),
      ),
    );
  }

  // Custom map style for better appearance
  static const String _mapStyle = '''
  [
    {
      "featureType": "poi",
      "elementType": "labels.text",
      "stylers": [
        {
          "visibility": "off"
        }
      ]
    },
    {
      "featureType": "poi.business",
      "stylers": [
        {
          "visibility": "off"
        }
      ]
    },
    {
      "featureType": "road",
      "elementType": "labels.icon",
      "stylers": [
        {
          "visibility": "off"
        }
      ]
    },
    {
      "featureType": "transit",
      "stylers": [
        {
          "visibility": "off"
        }
      ]
    }
  ]
  ''';

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
