import 'package:equatable/equatable.dart';

class LocationModel extends Equatable {
  final double latitude;
  final double longitude;
  final String? address;
  final String? name;
  final String? placeId;

  const LocationModel({
    required this.latitude,
    required this.longitude,
    this.address,
    this.name,
    this.placeId,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      address: json['address'],
      name: json['name'],
      placeId: json['placeId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'name': name,
      'placeId': placeId,
    };
  }

  LocationModel copyWith({
    double? latitude,
    double? longitude,
    String? address,
    String? name,
    String? placeId,
  }) {
    return LocationModel(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      name: name ?? this.name,
      placeId: placeId ?? this.placeId,
    );
  }

  @override
  List<Object?> get props => [latitude, longitude, address, name, placeId];
}

class RideModel extends Equatable {
  final String id;
  final String riderId;
  final String? driverId;
  final LocationModel pickupLocation;
  final LocationModel dropoffLocation;
  final String status;
  final double? fare;
  final String? paymentMethod;
  final DateTime requestedAt;
  final DateTime? acceptedAt;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final double? rating;
  final String? review;
  final Map<String, dynamic>? additionalData;

  const RideModel({
    required this.id,
    required this.riderId,
    this.driverId,
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.status,
    this.fare,
    this.paymentMethod,
    required this.requestedAt,
    this.acceptedAt,
    this.startedAt,
    this.completedAt,
    this.rating,
    this.review,
    this.additionalData,
  });

  factory RideModel.fromJson(Map<String, dynamic> json) {
    return RideModel(
      id: json['id'] ?? '',
      riderId: json['riderId'] ?? '',
      driverId: json['driverId'],
      pickupLocation: LocationModel.fromJson(json['pickupLocation'] ?? {}),
      dropoffLocation: LocationModel.fromJson(json['dropoffLocation'] ?? {}),
      status: json['status'] ?? 'requested',
      fare: json['fare']?.toDouble(),
      paymentMethod: json['paymentMethod'],
      requestedAt: DateTime.parse(json['requestedAt'] ?? DateTime.now().toIso8601String()),
      acceptedAt: json['acceptedAt'] != null ? DateTime.parse(json['acceptedAt']) : null,
      startedAt: json['startedAt'] != null ? DateTime.parse(json['startedAt']) : null,
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      rating: json['rating']?.toDouble(),
      review: json['review'],
      additionalData: json['additionalData'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'riderId': riderId,
      'driverId': driverId,
      'pickupLocation': pickupLocation.toJson(),
      'dropoffLocation': dropoffLocation.toJson(),
      'status': status,
      'fare': fare,
      'paymentMethod': paymentMethod,
      'requestedAt': requestedAt.toIso8601String(),
      'acceptedAt': acceptedAt?.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'rating': rating,
      'review': review,
      'additionalData': additionalData,
    };
  }

  RideModel copyWith({
    String? id,
    String? riderId,
    String? driverId,
    LocationModel? pickupLocation,
    LocationModel? dropoffLocation,
    String? status,
    double? fare,
    String? paymentMethod,
    DateTime? requestedAt,
    DateTime? acceptedAt,
    DateTime? startedAt,
    DateTime? completedAt,
    double? rating,
    String? review,
    Map<String, dynamic>? additionalData,
  }) {
    return RideModel(
      id: id ?? this.id,
      riderId: riderId ?? this.riderId,
      driverId: driverId ?? this.driverId,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      dropoffLocation: dropoffLocation ?? this.dropoffLocation,
      status: status ?? this.status,
      fare: fare ?? this.fare,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      requestedAt: requestedAt ?? this.requestedAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      rating: rating ?? this.rating,
      review: review ?? this.review,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  @override
  List<Object?> get props => [
        id,
        riderId,
        driverId,
        pickupLocation,
        dropoffLocation,
        status,
        fare,
        paymentMethod,
        requestedAt,
        acceptedAt,
        startedAt,
        completedAt,
        rating,
        review,
        additionalData,
      ];
}
