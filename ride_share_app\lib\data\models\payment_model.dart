import 'package:equatable/equatable.dart';

class PaymentResult extends Equatable {
  final bool success;
  final String? transactionId;
  final String? paymentMethod;
  final double? amount;
  final String? rideId;
  final DateTime? processedAt;
  final String? errorMessage;
  final Map<String, dynamic>? metadata;

  const PaymentResult({
    required this.success,
    this.transactionId,
    this.paymentMethod,
    this.amount,
    this.rideId,
    this.processedAt,
    this.errorMessage,
    this.metadata,
  });

  factory PaymentResult.fromJson(Map<String, dynamic> json) {
    return PaymentResult(
      success: json['success'] ?? false,
      transactionId: json['transactionId'],
      paymentMethod: json['paymentMethod'],
      amount: json['amount']?.toDouble(),
      rideId: json['rideId'],
      processedAt: json['processedAt'] != null 
          ? DateTime.parse(json['processedAt']) 
          : null,
      errorMessage: json['errorMessage'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'transactionId': transactionId,
      'paymentMethod': paymentMethod,
      'amount': amount,
      'rideId': rideId,
      'processedAt': processedAt?.toIso8601String(),
      'errorMessage': errorMessage,
      'metadata': metadata,
    };
  }

  PaymentResult copyWith({
    bool? success,
    String? transactionId,
    String? paymentMethod,
    double? amount,
    String? rideId,
    DateTime? processedAt,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentResult(
      success: success ?? this.success,
      transactionId: transactionId ?? this.transactionId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      amount: amount ?? this.amount,
      rideId: rideId ?? this.rideId,
      processedAt: processedAt ?? this.processedAt,
      errorMessage: errorMessage ?? this.errorMessage,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        success,
        transactionId,
        paymentMethod,
        amount,
        rideId,
        processedAt,
        errorMessage,
        metadata,
      ];
}

class PaymentMethod extends Equatable {
  final String id;
  final String type;
  final String displayName;
  final bool isDefault;
  final Map<String, dynamic> metadata;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const PaymentMethod({
    required this.id,
    required this.type,
    required this.displayName,
    this.isDefault = false,
    this.metadata = const {},
    this.createdAt,
    this.updatedAt,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      id: json['id'] ?? '',
      type: json['type'] ?? '',
      displayName: json['displayName'] ?? '',
      isDefault: json['isDefault'] ?? false,
      metadata: json['metadata'] ?? {},
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'displayName': displayName,
      'isDefault': isDefault,
      'metadata': metadata,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  PaymentMethod copyWith({
    String? id,
    String? type,
    String? displayName,
    bool? isDefault,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentMethod(
      id: id ?? this.id,
      type: type ?? this.type,
      displayName: displayName ?? this.displayName,
      isDefault: isDefault ?? this.isDefault,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        type,
        displayName,
        isDefault,
        metadata,
        createdAt,
        updatedAt,
      ];
}
