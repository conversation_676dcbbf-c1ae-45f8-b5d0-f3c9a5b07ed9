// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ride_share_app/core/theme/app_theme.dart';
import 'package:ride_share_app/presentation/widgets/custom_button.dart';

void main() {
  testWidgets('Custom button widget test', (WidgetTester tester) async {
    // Build a custom button
    await tester.pumpWidget(
      MaterialApp(
        theme: AppTheme.lightTheme,
        home: Scaffold(
          body: CustomButton(
            text: 'Test Button',
            onPressed: () {},
          ),
        ),
      ),
    );

    // Verify that the button is present
    expect(find.text('Test Button'), findsOneWidget);
    expect(find.byType(ElevatedButton), findsOneWidget);
  });

  test('App theme configuration test', () {
    // Test that our theme is properly configured
    final theme = AppTheme.lightTheme;
    expect(theme.useMaterial3, true);
    expect(theme.colorScheme.brightness, Brightness.light);
  });
}
