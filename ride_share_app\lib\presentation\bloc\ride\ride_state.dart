import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';
import '../../../data/models/location_model.dart';

abstract class RideState extends Equatable {
  const RideState();

  @override
  List<Object?> get props => [];
}

class RideInitial extends RideState {}

class RideLoading extends RideState {}

class RideLocationSelecting extends RideState {
  final LocationModel? pickupLocation;
  final LocationModel? dropoffLocation;
  final LocationModel? currentLocation;

  const RideLocationSelecting({
    this.pickupLocation,
    this.dropoffLocation,
    this.currentLocation,
  });

  @override
  List<Object?> get props => [pickupLocation, dropoffLocation, currentLocation];
}

class RideRouteCalculated extends RideState {
  final LocationModel pickupLocation;
  final LocationModel dropoffLocation;
  final double estimatedDistance;
  final double estimatedDuration;
  final double estimatedFare;
  final List<RideOption> rideOptions;

  const RideRouteCalculated({
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.estimatedDistance,
    required this.estimatedDuration,
    required this.estimatedFare,
    required this.rideOptions,
  });

  @override
  List<Object> get props => [
        pickupLocation,
        dropoffLocation,
        estimatedDistance,
        estimatedDuration,
        estimatedFare,
        rideOptions,
      ];
}

class RideRequesting extends RideState {
  final LocationModel pickupLocation;
  final LocationModel dropoffLocation;
  final String rideType;

  const RideRequesting({
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.rideType,
  });

  @override
  List<Object> get props => [pickupLocation, dropoffLocation, rideType];
}

class RideRequestedState extends RideState {
  final RideModel ride;

  const RideRequestedState({required this.ride});

  @override
  List<Object> get props => [ride];
}

class RideAcceptedState extends RideState {
  final RideModel ride;
  final DriverInfo driver;

  const RideAcceptedState({
    required this.ride,
    required this.driver,
  });

  @override
  List<Object> get props => [ride, driver];
}

class RideInProgress extends RideState {
  final RideModel ride;
  final DriverInfo driver;
  final LocationModel? driverLocation;

  const RideInProgress({
    required this.ride,
    required this.driver,
    this.driverLocation,
  });

  @override
  List<Object?> get props => [ride, driver, driverLocation];
}

class RideCompletedState extends RideState {
  final RideModel ride;
  final double finalFare;

  const RideCompletedState({
    required this.ride,
    required this.finalFare,
  });

  @override
  List<Object> get props => [ride, finalFare];
}

class RideRatedState extends RideState {
  final RideModel ride;
  final double rating;

  const RideRatedState({
    required this.ride,
    required this.rating,
  });

  @override
  List<Object> get props => [ride, rating];
}

class RideHistoryLoaded extends RideState {
  final List<RideModel> rides;

  const RideHistoryLoaded({required this.rides});

  @override
  List<Object> get props => [rides];
}

class RideError extends RideState {
  final String message;

  const RideError({required this.message});

  @override
  List<Object> get props => [message];
}

// Helper classes
class RideOption extends Equatable {
  final String type;
  final String name;
  final String description;
  final double fare;
  final int estimatedArrival; // in minutes
  final IconData icon;

  const RideOption({
    required this.type,
    required this.name,
    required this.description,
    required this.fare,
    required this.estimatedArrival,
    required this.icon,
  });

  @override
  List<Object> get props => [type, name, description, fare, estimatedArrival, icon];
}

class DriverInfo extends Equatable {
  final String id;
  final String name;
  final String phoneNumber;
  final double rating;
  final String vehicleModel;
  final String vehicleColor;
  final String licensePlate;
  final String? profileImageUrl;

  const DriverInfo({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.rating,
    required this.vehicleModel,
    required this.vehicleColor,
    required this.licensePlate,
    this.profileImageUrl,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        phoneNumber,
        rating,
        vehicleModel,
        vehicleColor,
        licensePlate,
        profileImageUrl,
      ];
}
