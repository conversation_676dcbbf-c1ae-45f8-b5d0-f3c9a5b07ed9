import 'package:flutter/material.dart';
import '../../widgets/custom_button.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/payment_model.dart';

class PaymentMethodsScreen extends StatefulWidget {
  const PaymentMethodsScreen({super.key});

  @override
  State<PaymentMethodsScreen> createState() => _PaymentMethodsScreenState();
}

class _PaymentMethodsScreenState extends State<PaymentMethodsScreen> {
  List<PaymentMethod> _paymentMethods = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPaymentMethods();
  }

  Future<void> _loadPaymentMethods() async {
    // Simulate loading payment methods
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _paymentMethods = [
        PaymentMethod(
          id: '1',
          type: 'card',
          displayName: 'Visa **** 1234',
          isDefault: true,
          metadata: {
            'last4': '1234',
            'brand': 'visa',
            'expiryMonth': '12',
            'expiryYear': '2025',
          },
        ),
        PaymentMethod(
          id: '2',
          type: 'card',
          displayName: 'Mastercard **** 5678',
          isDefault: false,
          metadata: {
            'last4': '5678',
            'brand': 'mastercard',
            'expiryMonth': '08',
            'expiryYear': '2026',
          },
        ),
        PaymentMethod(
          id: '3',
          type: 'wallet',
          displayName: 'RideShare Wallet',
          isDefault: false,
          metadata: {
            'balance': '45.20',
          },
        ),
      ];
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Methods'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: AppTheme.primaryColor,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _paymentMethods.length,
                    itemBuilder: (context, index) {
                      final method = _paymentMethods[index];
                      return _buildPaymentMethodCard(method);
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: CustomButton(
                    text: 'Add New Payment Method',
                    icon: Icons.add,
                    onPressed: _addNewPaymentMethod,
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildPaymentMethodCard(PaymentMethod method) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: ListTile(
          contentPadding: const EdgeInsets.all(16),
          leading: _getPaymentMethodIcon(method.type),
          title: Text(
            method.displayName,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (method.type == 'card') ...[
                Text('Expires ${method.metadata['expiryMonth']}/${method.metadata['expiryYear']}'),
              ] else if (method.type == 'wallet') ...[
                Text('Balance: \$${method.metadata['balance']}'),
              ],
              if (method.isDefault) ...[
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'DEFAULT',
                    style: TextStyle(
                      color: AppTheme.onPrimary,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
          trailing: PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(value, method),
            itemBuilder: (context) => [
              if (!method.isDefault)
                const PopupMenuItem(
                  value: 'set_default',
                  child: Text('Set as Default'),
                ),
              if (method.type == 'card')
                const PopupMenuItem(
                  value: 'edit',
                  child: Text('Edit'),
                ),
              if (method.type == 'wallet')
                const PopupMenuItem(
                  value: 'add_funds',
                  child: Text('Add Funds'),
                ),
              const PopupMenuItem(
                value: 'delete',
                child: Text('Delete'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _getPaymentMethodIcon(String type) {
    IconData iconData;
    Color color;

    switch (type) {
      case 'card':
        iconData = Icons.credit_card;
        color = AppTheme.primaryColor;
        break;
      case 'wallet':
        iconData = Icons.account_balance_wallet;
        color = AppTheme.successColor;
        break;
      default:
        iconData = Icons.payment;
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        color: color,
        size: 24,
      ),
    );
  }

  void _handleMenuAction(String action, PaymentMethod method) {
    switch (action) {
      case 'set_default':
        _setAsDefault(method);
        break;
      case 'edit':
        _editPaymentMethod(method);
        break;
      case 'add_funds':
        _addFunds(method);
        break;
      case 'delete':
        _deletePaymentMethod(method);
        break;
    }
  }

  void _setAsDefault(PaymentMethod method) {
    setState(() {
      for (var i = 0; i < _paymentMethods.length; i++) {
        _paymentMethods[i] = _paymentMethods[i].copyWith(
          isDefault: _paymentMethods[i].id == method.id,
        );
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${method.displayName} set as default'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _editPaymentMethod(PaymentMethod method) {
    // Navigate to edit screen or show edit dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Payment Method'),
        content: const Text('Edit functionality would be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _addFunds(PaymentMethod method) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Funds'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Current Balance: \$${method.metadata['balance']}'),
            const SizedBox(height: 16),
            const TextField(
              decoration: InputDecoration(
                labelText: 'Amount to Add',
                prefixText: '\$',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Funds added successfully!'),
                  backgroundColor: AppTheme.successColor,
                ),
              );
            },
            child: const Text('Add Funds'),
          ),
        ],
      ),
    );
  }

  void _deletePaymentMethod(PaymentMethod method) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Payment Method'),
        content: Text('Are you sure you want to delete ${method.displayName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _paymentMethods.removeWhere((m) => m.id == method.id);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${method.displayName} deleted'),
                  backgroundColor: AppTheme.error,
                ),
              );
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _addNewPaymentMethod() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Add Payment Method',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              ListTile(
                leading: const Icon(Icons.credit_card, color: AppTheme.primaryColor),
                title: const Text('Credit/Debit Card'),
                subtitle: const Text('Visa, Mastercard, American Express'),
                onTap: () {
                  Navigator.pop(context);
                  _addCreditCard();
                },
              ),
              ListTile(
                leading: const Icon(Icons.account_balance, color: AppTheme.infoColor),
                title: const Text('Bank Account'),
                subtitle: const Text('Link your bank account'),
                onTap: () {
                  Navigator.pop(context);
                  _addBankAccount();
                },
              ),
              ListTile(
                leading: const Icon(Icons.account_balance_wallet, color: AppTheme.successColor),
                title: const Text('Digital Wallet'),
                subtitle: const Text('PayPal, Apple Pay, Google Pay'),
                onTap: () {
                  Navigator.pop(context);
                  _addDigitalWallet();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addCreditCard() {
    // Navigate to add credit card screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add Credit Card functionality would be implemented here'),
      ),
    );
  }

  void _addBankAccount() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add Bank Account functionality would be implemented here'),
      ),
    );
  }

  void _addDigitalWallet() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add Digital Wallet functionality would be implemented here'),
      ),
    );
  }
}
