import 'package:equatable/equatable.dart';
import '../../../data/models/location_model.dart';

abstract class RideEvent extends Equatable {
  const RideEvent();

  @override
  List<Object?> get props => [];
}

class RideInitialized extends RideEvent {}

class PickupLocationSelected extends RideEvent {
  final LocationModel location;

  const PickupLocationSelected({required this.location});

  @override
  List<Object> get props => [location];
}

class DropoffLocationSelected extends RideEvent {
  final LocationModel location;

  const DropoffLocationSelected({required this.location});

  @override
  List<Object> get props => [location];
}

class RideRequested extends RideEvent {
  final String rideType;
  final String paymentMethod;

  const RideRequested({
    required this.rideType,
    required this.paymentMethod,
  });

  @override
  List<Object> get props => [rideType, paymentMethod];
}

class RideCancelled extends RideEvent {
  final String rideId;

  const RideCancelled({required this.rideId});

  @override
  List<Object> get props => [rideId];
}

class RideAccepted extends RideEvent {
  final String rideId;
  final String driverId;

  const RideAccepted({
    required this.rideId,
    required this.driverId,
  });

  @override
  List<Object> get props => [rideId, driverId];
}

class RideStarted extends RideEvent {
  final String rideId;

  const RideStarted({required this.rideId});

  @override
  List<Object> get props => [rideId];
}

class RideCompleted extends RideEvent {
  final String rideId;
  final double fare;

  const RideCompleted({
    required this.rideId,
    required this.fare,
  });

  @override
  List<Object> get props => [rideId, fare];
}

class RideRated extends RideEvent {
  final String rideId;
  final double rating;
  final String? review;

  const RideRated({
    required this.rideId,
    required this.rating,
    this.review,
  });

  @override
  List<Object?> get props => [rideId, rating, review];
}

class RideHistoryRequested extends RideEvent {}

class CurrentLocationRequested extends RideEvent {}

class RouteCalculated extends RideEvent {
  final LocationModel pickup;
  final LocationModel dropoff;

  const RouteCalculated({
    required this.pickup,
    required this.dropoff,
  });

  @override
  List<Object> get props => [pickup, dropoff];
}
