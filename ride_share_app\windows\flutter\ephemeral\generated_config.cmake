# Generated code do not commit.
file(TO_CMAKE_PATH "V:\\Program Files\\flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\project 1\\ride_share_app" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=V:\\Program Files\\flutter\\flutter"
  "PROJECT_DIR=D:\\project 1\\ride_share_app"
  "FLUTTER_ROOT=V:\\Program Files\\flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\project 1\\ride_share_app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\project 1\\ride_share_app"
  "FLUTTER_TARGET=D:\\project 1\\ride_share_app\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9jNGNkNDhlMTg2NDYwYjMyZDQ0NTg1Y2UzYzEwMzI3MWFiNjc2MzU1Lw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\project 1\\ride_share_app\\.dart_tool\\package_config.json"
)
