import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart';
import '../../bloc/auth/auth_event.dart';
import '../../bloc/ride/ride_bloc.dart';
import '../../bloc/ride/ride_state.dart';
import '../../bloc/ride/ride_event.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/map_widget.dart';
import '../../widgets/location_search_widget.dart';
import '../payment/payment_screen.dart';
import '../payment/payment_methods_screen.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/location_model.dart';

class RiderHomeScreen extends StatefulWidget {
  const RiderHomeScreen({super.key});

  @override
  State<RiderHomeScreen> createState() => _RiderHomeScreenState();
}

class _RiderHomeScreenState extends State<RiderHomeScreen> {
  final _pickupController = TextEditingController();
  final _dropoffController = TextEditingController();
  int _selectedIndex = 0;

  @override
  void dispose() {
    _pickupController.dispose();
    _dropoffController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _selectedIndex,
        children: [
          _buildHomeTab(),
          _buildHistoryTab(),
          _buildProfileTab(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history),
            label: 'History',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }

  Widget _buildHomeTab() {
    return BlocListener<RideBloc, RideState>(
      listener: (context, state) {
        if (state is RideError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppTheme.error,
            ),
          );
        } else if (state is RideRequestedState) {
          _showRideRequestedDialog();
        } else if (state is RideAcceptedState) {
          Navigator.of(context).pop(); // Close any open dialogs
          _showRideAcceptedDialog(state);
        }
      },
      child: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      BlocBuilder<AuthBloc, AuthState>(
                        builder: (context, state) {
                          if (state is AuthSuccess) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _getGreeting(),
                                  style: const TextStyle(
                                    color: AppTheme.onPrimary,
                                    fontSize: 16,
                                  ),
                                ),
                                Text(
                                  state.user.name,
                                  style: const TextStyle(
                                    color: AppTheme.onPrimary,
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            );
                          }
                          return const SizedBox();
                        },
                      ),
                      CircleAvatar(
                        backgroundColor: AppTheme.onPrimary,
                        child: const Icon(
                          Icons.person,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'Where would you like to go?',
                    style: TextStyle(
                      color: AppTheme.onPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            // Map Section
            Expanded(
              flex: 2,
              child: Container(
                margin: const EdgeInsets.all(20),
                child: BlocBuilder<RideBloc, RideState>(
                  builder: (context, state) {
                    LocationModel? pickupLocation;
                    LocationModel? dropoffLocation;
                    LocationModel? currentLocation;

                    if (state is RideLocationSelecting) {
                      pickupLocation = state.pickupLocation;
                      dropoffLocation = state.dropoffLocation;
                      currentLocation = state.currentLocation;
                    } else if (state is RideRouteCalculated) {
                      pickupLocation = state.pickupLocation;
                      dropoffLocation = state.dropoffLocation;
                    }

                    return MapWidget(
                      initialLocation: currentLocation,
                      pickupLocation: pickupLocation,
                      dropoffLocation: dropoffLocation,
                      showCurrentLocation: true,
                      height: double.infinity,
                    );
                  },
                ),
              ),
            ),

            // Location Input Section
            Container(
              padding: const EdgeInsets.all(20),
              child: BlocBuilder<RideBloc, RideState>(
                builder: (context, state) {
                  return Column(
                    children: [
                      // Pickup Location
                      LocationSearchWidget(
                        hintText: 'Pickup location',
                        prefixIcon: Icons.my_location,
                        onLocationSelected: (location) {
                          context.read<RideBloc>().add(
                            PickupLocationSelected(location: location),
                          );
                        },
                        initialLocation: state is RideLocationSelecting
                            ? state.pickupLocation
                            : null,
                      ),

                      const SizedBox(height: 12),

                      // Dropoff Location
                      LocationSearchWidget(
                        hintText: 'Where to?',
                        prefixIcon: Icons.location_on,
                        onLocationSelected: (location) {
                          context.read<RideBloc>().add(
                            DropoffLocationSelected(location: location),
                          );
                        },
                        initialLocation: state is RideLocationSelecting
                            ? state.dropoffLocation
                            : null,
                      ),

                      const SizedBox(height: 20),

                      // Request Ride Button or Route Info
                      if (state is RideRouteCalculated) ...[
                        _buildRouteInfo(state),
                        const SizedBox(height: 16),
                        CustomButton(
                          text: 'Choose Ride Type',
                          onPressed: () {
                            _showRideOptionsBottomSheet(state);
                          },
                        ),
                      ] else if (state is RideLoading) ...[
                        const CircularProgressIndicator(),
                      ] else ...[
                        CustomButton(
                          text: 'Set Locations',
                          onPressed: null, // Disabled until locations are set
                        ),
                      ],
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryTab() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Ride History',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView.builder(
                itemCount: 5, // Placeholder data
                itemBuilder: (context, index) {
                  return Card(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: ListTile(
                      leading: const CircleAvatar(
                        backgroundColor: AppTheme.primaryColor,
                        child: Icon(Icons.directions_car, color: AppTheme.onPrimary),
                      ),
                      title: Text('Ride #${index + 1}'),
                      subtitle: const Text('From: Home\nTo: Office'),
                      trailing: const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text('\$12.50', style: TextStyle(fontWeight: FontWeight.bold)),
                          Text('Completed', style: TextStyle(color: AppTheme.successColor)),
                        ],
                      ),
                      isThreeLine: true,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileTab() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Profile Header
            BlocBuilder<AuthBloc, AuthState>(
              builder: (context, state) {
                if (state is AuthSuccess) {
                  return Column(
                    children: [
                      CircleAvatar(
                        radius: 50,
                        backgroundColor: AppTheme.primaryColor,
                        child: Text(
                          state.user.name.isNotEmpty ? state.user.name[0].toUpperCase() : 'U',
                          style: const TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.onPrimary,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        state.user.name,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        state.user.email,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  );
                }
                return const SizedBox();
              },
            ),
            
            const SizedBox(height: 40),
            
            // Profile Options
            _buildProfileOption(
              icon: Icons.person,
              title: 'Edit Profile',
              onTap: () {},
            ),
            _buildProfileOption(
              icon: Icons.payment,
              title: 'Payment Methods',
              onTap: () {},
            ),
            _buildProfileOption(
              icon: Icons.notifications,
              title: 'Notifications',
              onTap: () {},
            ),
            _buildProfileOption(
              icon: Icons.help,
              title: 'Help & Support',
              onTap: () {},
            ),
            _buildProfileOption(
              icon: Icons.info,
              title: 'About',
              onTap: () {},
            ),
            
            const SizedBox(height: 40),
            
            // Logout Button
            CustomButton(
              text: 'Logout',
              isOutlined: true,
              onPressed: () {
                context.read<AuthBloc>().add(SignOutRequested());
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.primaryColor),
      title: Text(title),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good morning,';
    } else if (hour < 17) {
      return 'Good afternoon,';
    } else {
      return 'Good evening,';
    }
  }

  Widget _buildRouteInfo(RideRouteCalculated state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Column(
            children: [
              const Icon(Icons.straighten, color: AppTheme.primaryColor),
              const SizedBox(height: 4),
              Text(
                '${state.estimatedDistance.toStringAsFixed(1)} km',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const Text('Distance', style: TextStyle(fontSize: 12)),
            ],
          ),
          Column(
            children: [
              const Icon(Icons.access_time, color: AppTheme.primaryColor),
              const SizedBox(height: 4),
              Text(
                '${state.estimatedDuration.toStringAsFixed(0)} min',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const Text('Duration', style: TextStyle(fontSize: 12)),
            ],
          ),
          Column(
            children: [
              const Icon(Icons.attach_money, color: AppTheme.primaryColor),
              const SizedBox(height: 4),
              Text(
                '\$${state.estimatedFare.toStringAsFixed(2)}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const Text('Est. Fare', style: TextStyle(fontSize: 12)),
            ],
          ),
        ],
      ),
    );
  }

  void _showRideOptionsBottomSheet(RideRouteCalculated state) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Choose Ride Type',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ...state.rideOptions.map((option) =>
              _buildRideOptionCard(option, state)
            ).toList(),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildRideOptionCard(RideOption option, RideRouteCalculated state) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.pop(context);
            _showPaymentMethodDialog(option, state);
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(option.icon, size: 32, color: AppTheme.primaryColor),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        option.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        option.description,
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      Text(
                        '${option.estimatedArrival} min away',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '\$${option.fare.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showPaymentMethodDialog(RideOption option, RideRouteCalculated state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Payment Method'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.credit_card),
              title: const Text('Credit Card'),
              subtitle: const Text('**** **** **** 1234'),
              onTap: () {
                Navigator.pop(context);
                _requestRide(option.type, 'card');
              },
            ),
            ListTile(
              leading: const Icon(Icons.account_balance_wallet),
              title: const Text('Wallet'),
              subtitle: const Text('Balance: \$45.20'),
              onTap: () {
                Navigator.pop(context);
                _requestRide(option.type, 'wallet');
              },
            ),
            ListTile(
              leading: const Icon(Icons.money),
              title: const Text('Cash'),
              subtitle: const Text('Pay with cash'),
              onTap: () {
                Navigator.pop(context);
                _requestRide(option.type, 'cash');
              },
            ),
          ],
        ),
      ),
    );
  }

  void _requestRide(String rideType, String paymentMethod) {
    context.read<RideBloc>().add(
      RideRequested(
        rideType: rideType,
        paymentMethod: paymentMethod,
      ),
    );
  }



  void _showRideRequestedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Ride Requested'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Looking for nearby drivers...'),
            SizedBox(height: 8),
            Text(
              'This may take a few moments',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<RideBloc>().add(RideCancelled(rideId: 'current_ride'));
            },
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showRideAcceptedDialog(RideAcceptedState state) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Driver Found!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: AppTheme.primaryColor,
              child: Text(
                state.driver.name[0].toUpperCase(),
                style: const TextStyle(
                  color: AppTheme.onPrimary,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              state.driver.name,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 16),
                Text(' ${state.driver.rating}'),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Vehicle:'),
                      Text(
                        '${state.driver.vehicleColor} ${state.driver.vehicleModel}',
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('License:'),
                      Text(
                        state.driver.licensePlate,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Your driver is on the way!',
              style: TextStyle(
                color: AppTheme.successColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              // Call driver functionality
            },
            child: const Text('Call Driver'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<RideBloc>().add(RideCancelled(rideId: state.ride.id));
            },
            child: const Text('Cancel Ride'),
          ),
        ],
      ),
    );
  }
}
