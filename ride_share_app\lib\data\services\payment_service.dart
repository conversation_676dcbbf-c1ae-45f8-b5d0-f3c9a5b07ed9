import 'package:flutter/foundation.dart';
import '../models/payment_model.dart';
import '../../core/constants/app_constants.dart';

class PaymentService {
  // Simulate payment processing
  Future<PaymentResult> processPayment({
    required double amount,
    required String paymentMethod,
    required String rideId,
    String? cardToken,
  }) async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 2));

      switch (paymentMethod) {
        case AppConstants.paymentCard:
          return await _processCardPayment(amount, cardToken, rideId);
        case AppConstants.paymentWallet:
          return await _processWalletPayment(amount, rideId);
        case AppConstants.paymentCash:
          return _processCashPayment(amount, rideId);
        default:
          throw Exception('Unsupported payment method: $paymentMethod');
      }
    } catch (e) {
      return PaymentResult(
        success: false,
        transactionId: null,
        errorMessage: e.toString(),
      );
    }
  }

  Future<PaymentResult> _processCardPayment(
    double amount,
    String? cardToken,
    String rideId,
  ) async {
    // Simulate card payment processing
    if (cardToken == null || cardToken.isEmpty) {
      throw Exception('Card token is required for card payments');
    }

    // Simulate payment gateway response
    final success = amount < 1000; // Simulate failure for large amounts
    
    if (success) {
      return PaymentResult(
        success: true,
        transactionId: 'txn_${DateTime.now().millisecondsSinceEpoch}',
        paymentMethod: AppConstants.paymentCard,
        amount: amount,
        rideId: rideId,
        processedAt: DateTime.now(),
      );
    } else {
      throw Exception('Card payment failed. Please try a different card.');
    }
  }

  Future<PaymentResult> _processWalletPayment(
    double amount,
    String rideId,
  ) async {
    // Check wallet balance (mock)
    final walletBalance = await _getWalletBalance();
    
    if (walletBalance < amount) {
      throw Exception('Insufficient wallet balance. Please add funds.');
    }

    // Deduct from wallet
    await _deductFromWallet(amount);

    return PaymentResult(
      success: true,
      transactionId: 'wallet_${DateTime.now().millisecondsSinceEpoch}',
      paymentMethod: AppConstants.paymentWallet,
      amount: amount,
      rideId: rideId,
      processedAt: DateTime.now(),
    );
  }

  PaymentResult _processCashPayment(double amount, String rideId) {
    // Cash payments are processed immediately
    return PaymentResult(
      success: true,
      transactionId: 'cash_${DateTime.now().millisecondsSinceEpoch}',
      paymentMethod: AppConstants.paymentCash,
      amount: amount,
      rideId: rideId,
      processedAt: DateTime.now(),
    );
  }

  Future<double> _getWalletBalance() async {
    // Mock wallet balance
    return 45.20;
  }

  Future<void> _deductFromWallet(double amount) async {
    // Mock wallet deduction
    debugPrint('Deducted \$${amount.toStringAsFixed(2)} from wallet');
  }

  Future<List<PaymentMethod>> getPaymentMethods() async {
    // Mock payment methods
    return [
      PaymentMethod(
        id: 'card_1',
        type: AppConstants.paymentCard,
        displayName: 'Visa **** 1234',
        isDefault: true,
        metadata: {
          'last4': '1234',
          'brand': 'visa',
          'expiryMonth': '12',
          'expiryYear': '2025',
        },
      ),
      PaymentMethod(
        id: 'wallet_1',
        type: AppConstants.paymentWallet,
        displayName: 'RideShare Wallet',
        isDefault: false,
        metadata: {
          'balance': '45.20',
        },
      ),
      PaymentMethod(
        id: 'cash_1',
        type: AppConstants.paymentCash,
        displayName: 'Cash Payment',
        isDefault: false,
        metadata: {},
      ),
    ];
  }

  Future<PaymentMethod> addPaymentMethod({
    required String type,
    required Map<String, dynamic> details,
  }) async {
    // Simulate adding a new payment method
    await Future.delayed(const Duration(seconds: 1));

    return PaymentMethod(
      id: 'new_${DateTime.now().millisecondsSinceEpoch}',
      type: type,
      displayName: _generateDisplayName(type, details),
      isDefault: false,
      metadata: details,
    );
  }

  String _generateDisplayName(String type, Map<String, dynamic> details) {
    switch (type) {
      case AppConstants.paymentCard:
        final brand = details['brand'] ?? 'Card';
        final last4 = details['last4'] ?? '****';
        return '$brand **** $last4';
      case AppConstants.paymentWallet:
        return 'RideShare Wallet';
      case AppConstants.paymentCash:
        return 'Cash Payment';
      default:
        return 'Unknown Payment Method';
    }
  }

  Future<void> removePaymentMethod(String paymentMethodId) async {
    // Simulate removing payment method
    await Future.delayed(const Duration(milliseconds: 500));
    debugPrint('Removed payment method: $paymentMethodId');
  }

  Future<void> setDefaultPaymentMethod(String paymentMethodId) async {
    // Simulate setting default payment method
    await Future.delayed(const Duration(milliseconds: 500));
    debugPrint('Set default payment method: $paymentMethodId');
  }

  Future<List<PaymentResult>> getPaymentHistory({
    int limit = 20,
    String? userId,
  }) async {
    // Mock payment history
    await Future.delayed(const Duration(seconds: 1));

    return List.generate(10, (index) {
      return PaymentResult(
        success: true,
        transactionId: 'txn_${1000 + index}',
        paymentMethod: index % 3 == 0 
            ? AppConstants.paymentCard 
            : index % 3 == 1 
                ? AppConstants.paymentWallet 
                : AppConstants.paymentCash,
        amount: 15.50 + (index * 2.5),
        rideId: 'ride_${100 + index}',
        processedAt: DateTime.now().subtract(Duration(days: index)),
      );
    });
  }

  Future<PaymentResult> refundPayment({
    required String transactionId,
    required double amount,
    String? reason,
  }) async {
    // Simulate refund processing
    await Future.delayed(const Duration(seconds: 2));

    return PaymentResult(
      success: true,
      transactionId: 'refund_${DateTime.now().millisecondsSinceEpoch}',
      paymentMethod: 'refund',
      amount: -amount, // Negative amount for refund
      rideId: 'refund_for_$transactionId',
      processedAt: DateTime.now(),
      metadata: {
        'originalTransaction': transactionId,
        'reason': reason ?? 'Ride cancelled',
      },
    );
  }
}
