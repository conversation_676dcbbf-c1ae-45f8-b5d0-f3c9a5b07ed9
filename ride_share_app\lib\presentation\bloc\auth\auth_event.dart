import 'package:equatable/equatable.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthStarted extends AuthEvent {}

class SignInRequested extends AuthEvent {
  final String email;
  final String password;

  const SignInRequested({
    required this.email,
    required this.password,
  });

  @override
  List<Object> get props => [email, password];
}

class SignUpRequested extends AuthEvent {
  final String email;
  final String password;
  final String name;
  final String userType;
  final String? phoneNumber;

  const SignUpRequested({
    required this.email,
    required this.password,
    required this.name,
    required this.userType,
    this.phoneNumber,
  });

  @override
  List<Object?> get props => [email, password, name, userType, phoneNumber];
}

class GoogleSignInRequested extends AuthEvent {
  final String userType;

  const GoogleSignInRequested({required this.userType});

  @override
  List<Object> get props => [userType];
}

class SignOutRequested extends AuthEvent {}

class PasswordResetRequested extends AuthEvent {
  final String email;

  const PasswordResetRequested({required this.email});

  @override
  List<Object> get props => [email];
}

class ProfileUpdateRequested extends AuthEvent {
  final String? name;
  final String? phoneNumber;
  final String? profileImageUrl;

  const ProfileUpdateRequested({
    this.name,
    this.phoneNumber,
    this.profileImageUrl,
  });

  @override
  List<Object?> get props => [name, phoneNumber, profileImageUrl];
}
