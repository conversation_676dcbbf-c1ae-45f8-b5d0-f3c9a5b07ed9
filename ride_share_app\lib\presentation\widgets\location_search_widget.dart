import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import '../../data/models/location_model.dart';
import '../../core/theme/app_theme.dart';

class LocationSearchWidget extends StatefulWidget {
  final String hintText;
  final IconData prefixIcon;
  final Function(LocationModel) onLocationSelected;
  final LocationModel? initialLocation;
  final bool enabled;

  const LocationSearchWidget({
    super.key,
    required this.hintText,
    required this.prefixIcon,
    required this.onLocationSelected,
    this.initialLocation,
    this.enabled = true,
  });

  @override
  State<LocationSearchWidget> createState() => _LocationSearchWidgetState();
}

class _LocationSearchWidgetState extends State<LocationSearchWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<LocationModel> _searchResults = [];
  bool _isSearching = false;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    if (widget.initialLocation != null) {
      _controller.text = widget.initialLocation!.address ?? 
                        widget.initialLocation!.name ?? 
                        '${widget.initialLocation!.latitude}, ${widget.initialLocation!.longitude}';
    }
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus) {
      _showOverlay();
    } else {
      _removeOverlay();
    }
  }

  void _showOverlay() {
    _removeOverlay();
    
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: MediaQuery.of(context).size.width - 40,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, 60),
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: _buildSearchResults(),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  final LayerLink _layerLink = LayerLink();

  Widget _buildSearchResults() {
    if (_isSearching) {
      return const Padding(
        padding: EdgeInsets.all(16),
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16),
        child: Text(
          'Start typing to search for locations...',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final location = _searchResults[index];
        return ListTile(
          leading: Icon(
            Icons.location_on,
            color: AppTheme.primaryColor,
          ),
          title: Text(
            location.name ?? 'Unknown Location',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          subtitle: Text(
            location.address ?? '${location.latitude}, ${location.longitude}',
            style: TextStyle(color: Colors.grey[600]),
          ),
          onTap: () {
            _selectLocation(location);
          },
        );
      },
    );
  }

  void _selectLocation(LocationModel location) {
    _controller.text = location.address ?? location.name ?? 'Selected Location';
    _focusNode.unfocus();
    _removeOverlay();
    widget.onLocationSelected(location);
  }

  Future<void> _searchLocations(String query) async {
    if (query.length < 3) {
      setState(() {
        _searchResults = [];
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      // Use geocoding to search for locations
      List<Location> locations = await locationFromAddress(query);
      
      List<LocationModel> results = [];
      for (Location location in locations.take(5)) {
        // Get address from coordinates
        List<Placemark> placemarks = await placemarkFromCoordinates(
          location.latitude,
          location.longitude,
        );
        
        if (placemarks.isNotEmpty) {
          Placemark place = placemarks.first;
          String address = _formatAddress(place);
          
          results.add(LocationModel(
            latitude: location.latitude,
            longitude: location.longitude,
            address: address,
            name: place.name ?? place.locality ?? 'Unknown',
          ));
        }
      }

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      debugPrint('Error searching locations: $e');
    }
  }

  String _formatAddress(Placemark place) {
    List<String> addressParts = [];
    
    if (place.name != null && place.name!.isNotEmpty) {
      addressParts.add(place.name!);
    }
    if (place.street != null && place.street!.isNotEmpty) {
      addressParts.add(place.street!);
    }
    if (place.locality != null && place.locality!.isNotEmpty) {
      addressParts.add(place.locality!);
    }
    if (place.administrativeArea != null && place.administrativeArea!.isNotEmpty) {
      addressParts.add(place.administrativeArea!);
    }
    
    return addressParts.join(', ');
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _controller,
          focusNode: _focusNode,
          enabled: widget.enabled,
          onChanged: _searchLocations,
          decoration: InputDecoration(
            hintText: widget.hintText,
            hintStyle: TextStyle(color: Colors.grey[500]),
            prefixIcon: Icon(
              widget.prefixIcon,
              color: widget.prefixIcon == Icons.my_location 
                  ? AppTheme.primaryColor 
                  : AppTheme.error,
              size: 20,
            ),
            suffixIcon: _controller.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear, size: 20),
                    onPressed: () {
                      _controller.clear();
                      setState(() {
                        _searchResults = [];
                      });
                    },
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          style: const TextStyle(
            fontSize: 16,
            color: AppTheme.onSurface,
          ),
        ),
      ),
    );
  }
}
