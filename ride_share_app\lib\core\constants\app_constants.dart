class AppConstants {
  // App Info
  static const String appName = 'RideShare';
  static const String appVersion = '1.0.0';
  
  // API URLs
  static const String baseUrl = 'https://api.rideshare.com';
  static const String googleMapsApiKey = 'YOUR_GOOGLE_MAPS_API_KEY';
  
  // Firebase Collections
  static const String usersCollection = 'users';
  static const String ridesCollection = 'rides';
  static const String driversCollection = 'drivers';
  
  // User Types
  static const String userTypeRider = 'rider';
  static const String userTypeDriver = 'driver';
  
  // Ride Status
  static const String rideStatusRequested = 'requested';
  static const String rideStatusAccepted = 'accepted';
  static const String rideStatusInProgress = 'in_progress';
  static const String rideStatusCompleted = 'completed';
  static const String rideStatusCancelled = 'cancelled';
  
  // Payment Methods
  static const String paymentCash = 'cash';
  static const String paymentCard = 'card';
  static const String paymentWallet = 'wallet';
  
  // Notification Types
  static const String notificationRideRequest = 'ride_request';
  static const String notificationRideAccepted = 'ride_accepted';
  static const String notificationRideCompleted = 'ride_completed';
  
  // Map Settings
  static const double defaultZoom = 15.0;
  static const double maxZoom = 20.0;
  static const double minZoom = 10.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 300);
  static const Duration mediumAnimation = Duration(milliseconds: 500);
  static const Duration longAnimation = Duration(milliseconds: 800);
}
